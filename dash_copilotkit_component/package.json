{"name": "dash_copilotkit_component", "version": "0.0.1", "description": "Project Description", "repository": {"type": "git", "url": "git://github.com/dash-copilotkit/dash-copilotkit-component.git"}, "bugs": {"url": "https://github.com/dash-copilotkit/dash-copilotkit-component/issues"}, "homepage": "https://github.com/dash-copilotkit/dash-copilotkit-component", "main": "build/index.js", "scripts": {"start": "webpack serve --config ./webpack.serve.config.js --open", "validate-init": "python _validate_init.py", "prepublishOnly": "npm run validate-init", "build:js": "webpack --mode production", "build:backends": "dash-generate-components ./src/lib/components dash_copilotkit_component -p package-info.json --r-prefix '' --jl-prefix '' --ignore \\.test\\.", "build:backends-activated": "(. venv/bin/activate || venv\\scripts\\activate && npm run build:backends)", "build": "npm run build:js && npm run build:backends", "build:activated": "npm run build:js && npm run build:backends-activated"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"ramda": "^0.26.1"}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/preset-env": "^7.22.2", "@babel/preset-react": "^7.22.3", "@plotly/dash-component-plugins": "^1.2.3", "@plotly/webpack-dash-dynamic-import": "^1.2.0", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.2", "copyfiles": "^2.1.1", "css-loader": "^6.8.1", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.18.0", "eslint-plugin-react": "^7.14.2", "prop-types": "^15.8.1", "react": "^18.3.1", "react-docgen": "^5.4.3", "react-dom": "^18.3.1", "style-loader": "^3.3.3", "styled-jsx": "^3.2.1", "webpack": "^5.84.1", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0", "@copilotkit/react-core": "^latest", "@copilotkit/react-ui": "^latest"}, "engines": {"node": ">=8.11.0", "npm": ">=6.1.0"}}